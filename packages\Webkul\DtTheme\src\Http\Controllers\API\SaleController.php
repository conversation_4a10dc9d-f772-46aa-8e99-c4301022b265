<?php

namespace Webkul\DtTheme\Http\Controllers\API;

use Illuminate\Http\Resources\Json\JsonResource;
use Webkul\Customer\Repositories\CustomerRepository;
use Webkul\Product\Models\ProductFlat;
use Webkul\Shop\Http\Controllers\Controller;
use Webkul\Shop\Http\Resources\ProductResource;

class SaleController extends Controller
{
    public function __construct(
        protected CustomerRepository $customerRepository
    ) {}

    /**
     * Get products that are on sale (have a special price).
     */
    public function index(): JsonResource
    {
        $currentChannel = core()->getCurrentChannel();
        $today = Carbon::today()->toDateString();

        $query = ProductFlat::query()
            ->with(['product.images', 'product.videos', 'product.reviews'])
            ->where('channel', $currentChannel->code)
            ->where('locale', app()->getLocale())
            ->where('status', 1)
            ->where('visible_individually', 1)
            ->whereNotNull('special_price')
            ->where('special_price', '>', 0)
            ->where(function ($q) use ($today) {
                $q->whereNull('special_price_from')
                ->orWhere('special_price_from', '<=', $today);
            })
            ->where(function ($q) use ($today) {
                $q->whereNull('special_price_to')
                ->orWhere('special_price_to', '>=', $today);
            });

        $sortBy = request()->get('sort', 'created_at');
        $sortOrder = request()->get('order', 'desc');

        if ($sortBy === 'price') {
            $query->orderBy('special_price', $sortOrder);
        } else {
            $query->orderBy($sortBy, $sortOrder);
        }

        $products = $query->paginate(request()->get('limit', 12));

        return ProductResource::collection($products);
    }
}