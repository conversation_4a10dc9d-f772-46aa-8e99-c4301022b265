{{-- Sale Products Page --}}

<x-shop::layouts :has-feature="false">
    <!-- Page Title -->
    <x-slot:title>
        Sale Products
    </x-slot>

    <div class="container px-[60px] max-lg:px-8 max-sm:px-4">
        <div class="mt-8 flex items-center justify-between max-md:mt-5">
            <h1 class="text-2xl font-medium max-sm:text-base">
                Sale Products
            </h1>
        </div>
    </div>

    <!-- Product Listing -->
    <v-sale-products>
        <x-shop::shimmer.categories.view />
    </v-sale-products>

    @pushOnce('scripts')
        <script
            type="text/x-template"
            id="v-sale-products-template"
        >
            <div class="container px-[60px] max-lg:px-8 max-sm:px-4">
                <div class="flex items-start gap-10 max-lg:gap-5 md:mt-10">
                    <!-- Product Listing Container -->
                    <div class="flex-1">
                        <!-- Desktop Product Listing Toolbar -->
                        <div class="max-md:hidden">
                            @include('shop::categories.toolbar')
                        </div>

                        <!-- Product List Card Container -->
                        <div
                            class="mt-8 grid grid-cols-1 gap-6"
                            v-if="(filters.toolbar.applied.mode ?? filters.toolbar.default.mode) === 'list'"
                        >
                            <!-- Product Card Shimmer Effect -->
                            <template v-if="isLoading">
                                <x-shop::shimmer.products.cards.list count="12" />
                            </template>

                            <!-- Product Card Listing -->
                            <template v-else>
                                <template v-if="products.length">
                                    <x-shop::products.card
                                        ::mode="'list'"
                                        v-for="product in products"
                                    />
                                </template>

                                <!-- Empty Products Container -->
                                <template v-else>
                                    <div class="m-auto grid w-full place-content-center items-center justify-items-center py-32 text-center">
                                        <img
                                            class="max-sm:h-[100px] max-sm:w-[100px]"
                                            src="{{ bagisto_asset('images/thank-you.png') }}"
                                            alt="No sale products"
                                        />

                                        <p
                                            class="text-xl max-sm:text-sm"
                                            role="heading"
                                        >
                                            No products are currently on sale.
                                        </p>
                                    </div>
                                </template>
                            </template>
                        </div>

                        <!-- Product Grid Card Container -->
                        <div
                            class="mt-8 grid grid-cols-3 gap-8 max-1060:grid-cols-2 max-md:mt-5 max-md:justify-items-center max-md:gap-x-4 max-md:gap-y-5"
                            v-else
                        >
                            <!-- Product Card Shimmer Effect -->
                            <template v-if="isLoading">
                                <x-shop::shimmer.products.cards.grid count="12" />
                            </template>

                            <!-- Product Card Listing -->
                            <template v-else>
                                <template v-if="products.length">
                                    <x-shop::products.card
                                        ::mode="'grid'"
                                        v-for="product in products"
                                        :navigation-link="route('shop.sale')"
                                    />
                                </template>

                                <!-- Empty Products Container -->
                                <template v-else>
                                    <div class="m-auto grid w-full place-content-center items-center justify-items-center py-32 text-center">
                                        <img
                                            class="max-sm:h-[100px] max-sm:w-[100px]"
                                            src="{{ bagisto_asset('images/thank-you.png') }}"
                                            alt="No sale products"
                                        />

                                        <p
                                            class="text-xl max-sm:text-sm"
                                            role="heading"
                                        >
                                            No products are currently on sale.
                                        </p>
                                    </div>
                                </template>
                            </template>
                        </div>

                        <!-- Load More Button -->
                        <button
                            class="secondary-button mx-auto mt-14 block w-max rounded-2xl px-11 py-3 text-center text-base max-md:mt-8 max-md:px-8 max-md:py-2 max-md:text-sm"
                            @click="loadMoreProducts"
                            v-if="links.next"
                        >
                            @lang('shop::app.categories.view.load-more')
                        </button>
                    </div>
                </div>
            </div>
        </script>

        <script type="module">
            app.component('v-sale-products', {
                template: '#v-sale-products-template',

                data() {
                    return {
                        isMobile: window.innerWidth <= 767,

                        isLoading: true,

                        filters: {
                            toolbar: {
                                default: {},
                                applied: {},
                            },
                        },

                        products: [],

                        links: {},
                    }
                },

                computed: {
                    queryParams() {
                        return this.filters.toolbar.applied;
                    },
                },

                watch: {
                    queryParams() {
                        this.getProducts();
                    },
                },

                mounted() {
                    this.getProducts();
                },

                methods: {
                    setFilters(type, filters) {
                        this.filters[type] = filters;
                    },

                    getProducts() {
                        this.isLoading = true;

                        this.$axios.get("{{ route('shop.api.sale.products.index') }}", {
                            params: this.queryParams
                        })
                            .then(response => {
                                this.isLoading = false;
                                this.products = response.data.data;
                                this.links = response.data.links;
                            }).catch(error => {
                                this.isLoading = false;
                                console.log(error);
                            });
                    },

                    loadMoreProducts() {
                        if (this.links.next) {
                            this.$axios.get(this.links.next).then(response => {
                                this.products = [...this.products, ...response.data.data];
                                this.links = response.data.links;
                            }).catch(error => {
                                console.log(error);
                            });
                        }
                    },
                }
            });
        </script>
    @endPushOnce
</x-shop::layouts>
