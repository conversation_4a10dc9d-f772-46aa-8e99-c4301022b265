<?php

namespace Webkul\DtTheme\CacheFilters;

use Intervention\Image\Filters\FilterInterface;
use Intervention\Image\Image;

// Resizes images to exactly 144px wide while maintaining aspect ratio

class ProductThumbs implements FilterInterface
{
    /**
     * Apply filter - Resizes image to exactly 144px wide
     *
     * @return \Intervention\Image\Image
     */
    public function applyFilter(Image $image)
    {
        return $this->resizeToWidth($image, 144);
    }

    /**
     * Resize image to exact width while maintaining aspect ratio
     *
     * @param \Intervention\Image\Image $image
     * @param int $width
     * @return \Intervention\Image\Image
     */
    private function resizeToWidth(Image $image, int $width)
    {
        // Get original dimensions
        $originalWidth = $image->width();
        $originalHeight = $image->height();

        // If image is already the target width, return as is
        if ($originalWidth == $width) {
            return $image;
        }

        // Calculate new height based on aspect ratio
        $aspectRatio = $originalHeight / $originalWidth;
        $newHeight = (int) ($width * $aspectRatio);

        // Resize image to exact width while maintaining aspect ratio
        return $image->resize($width, $newHeight);
    }
}
