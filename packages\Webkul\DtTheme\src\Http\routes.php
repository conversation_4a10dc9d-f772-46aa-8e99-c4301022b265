<?php

use Illuminate\Support\Facades\Route;
use Webkul\DtTheme\Http\Controllers\SaleController;
use Webkul\DtTheme\Http\Controllers\API\SaleController as APISaleController;

Route::get('/sale', [SaleController::class, 'index'])->name('shop.sale');

// API route for sale products
Route::prefix('api')->group(function () {
    Route::get('/sale/products', [APISaleController::class, 'index'])->name('shop.api.sale.products.index');
});
